import {
	SlashCommandBuilder,
	ChatInputCommandInteraction,
	ButtonBuilder,
	ActionRowBuilder,
	ButtonStyle,
	ChannelType,
	PermissionFlagsBits,
	underline,
	bold,
	MessageFlags,
	ApplicationIntegrationType,
	InteractionContextType,
	ContainerBuilder,
	TextDisplayBuilder,
	MediaGalleryBuilder,
	MediaGalleryItemBuilder,
	SeparatorBuilder,
	SeparatorSpacingSize,
	TextChannel,
	NewsChannel,
} from "discord.js";
import { emojis } from "../../utilities/emojis.js";
import { basePermissions } from "../../utilities/permissions.js";
import { checkAppPermissions } from "../../handlers/index.js";
import {
	saveStaffRoleId,
	setupLoggingChannel,
} from "../../utilities/database.js";

export default {
	data: new SlashCommandBuilder()
		.setName("setup")
		.setDescription("Setup things!")
		.setDefaultMemberPermissions(PermissionFlagsBits.ManageGuild)
		.setIntegrationTypes([ApplicationIntegrationType.GuildInstall])
		.setContexts([InteractionContextType.Guild])
		.addSubcommand((sub) =>
			sub
				.setName("ticket")
				.addRoleOption((opt) =>
					opt.setName("staff_role").setRequired(true),
				)
				.addChannelOption((opt) =>
					opt
						.setName("channel")
						.addChannelTypes(ChannelType.GuildText)
						.setRequired(true),
				)
				.addStringOption((opt) => opt.setName("description"))
				.addAttachmentOption((opt) => opt.setName("image")),
		)
		.addSubcommand((sub) =>
			sub
				.setName("logs")
				.addChannelOption((opt) =>
					opt
						.setName("channel")
						.addChannelTypes(
							ChannelType.GuildText,
							ChannelType.PublicThread,
						)
						.setRequired(true),
				),
		),
	async execute(interaction: ChatInputCommandInteraction): Promise<void> {
		if (
			interaction.inGuild() &&
			!(await checkAppPermissions(interaction, basePermissions))
		)
			return;

		const guild = interaction.guild!;
		const sub = interaction.options.getSubcommand();

		if (sub === "ticket") {
			await interaction.deferReply({ flags: MessageFlags.Ephemeral });

			const staffRole = interaction.options.getRole(
				"staff_role",
				true,
			).id;
			const banner = interaction.options.getAttachment("image");
			const sendingChannel = interaction.options.getChannel(
				"channel",
				true,
			);

			if (!(sendingChannel instanceof TextChannel)) {
				await interaction.editReply({
					content: `${emojis.danger} Selected channel is not a text channel.`,
				});
				return;
			}

			if (
				!sendingChannel
					.permissionsFor(guild.members.me!)
					.has([
						PermissionFlagsBits.ViewChannel,
						PermissionFlagsBits.SendMessages,
					])
			) {
				await interaction.editReply({
					content: `${emojis.danger} I don't have permission in ${sendingChannel}!`,
				});
				return;
			}

			const container = new ContainerBuilder()
				.setAccentColor(0xa2845e)
				.addTextDisplayComponents(
					new TextDisplayBuilder().setContent(
						interaction.options.getString("description") ||
							`# ${emojis.button} Create a Ticket\nUse the button below to open a support thread.`,
					),
				)
				.addSeparatorComponents(
					new SeparatorBuilder()
						.setSpacing(SeparatorSpacingSize.Large)
						.setDivider(true),
				)
				.addMediaGalleryComponents(
					new MediaGalleryBuilder().addItems(
						new MediaGalleryItemBuilder().setURL(
							banner?.url ??
								"https://media.discordapp.net/...default.png",
						),
					),
				);

			const row = new ActionRowBuilder<ButtonBuilder>().addComponents(
				new ButtonBuilder()
					.setCustomId("create-ticket")
					.setLabel("Create ticket")
					.setStyle(ButtonStyle.Secondary)
					.setEmoji(emojis.ticket),
			);

			await interaction.editReply({
				content: `${emojis.ticketCreated} Ticket system setup successfully!`,
			});

			await saveStaffRoleId(guild.id, staffRole);
			await sendingChannel.send({
				components: [container, row],
				flags: MessageFlags.IsComponentsV2,
			});

			if (
				!guild.members.me!.permissions.has(
					PermissionFlagsBits.ManageMessages,
				)
			) {
				await interaction.followUp({
					content: `## ${emojis.danger} ${underline(
						"Recommending",
					)}\nGrant ${bold(
						"Manage Messages",
					)} so staff can pin the button message.`,
					flags: MessageFlags.Ephemeral,
				});
			}
		}

		if (sub === "logs") {
			await interaction.deferReply({ flags: MessageFlags.Ephemeral });

			const loggingChannel = interaction.options.getChannel(
				"channel",
				true,
			);

			if (
				!(
					loggingChannel instanceof TextChannel ||
					loggingChannel instanceof NewsChannel
				)
			) {
				await interaction.editReply({
					content: `${emojis.danger} Selected channel cannot receive logs.`,
				});
				return;
			}

			if (
				!loggingChannel
					.permissionsFor(guild.members.me!)
					.has(PermissionFlagsBits.SendMessages)
			) {
				await interaction.editReply({
					content: `${emojis.danger} I can't send messages in ${loggingChannel}!`,
				});
				return;
			}

			await setupLoggingChannel(guild.id, loggingChannel.id);

			await loggingChannel.send({
				content: `${emojis.info} Logs channel is now set up.`,
			});

			await interaction.editReply({
				content: `### ${emojis.info} Done!\nI will log events there instead of Audit Logs.`,
			});
		}
	},
} as const;
